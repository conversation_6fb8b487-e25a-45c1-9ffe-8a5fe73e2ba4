# Production Environment Configuration for LawVriksh
# Domain: https://lawvriksh.com
# API: https://lawvriksh.com/api
# Deployment: Kali Linux VPS with Docker

# =============================================================================
# DOMAIN CONFIGURATION
# =============================================================================
DOMAIN=lawvriksh.com
FRONTEND_URL=https://lawvriksh.com
API_BASE_URL=https://lawvriksh.com/api

# =============================================================================
# DATABASE CONFIGURATION (MySQL on custom port 3307)
# =============================================================================
DB_HOST=mysql
DB_PORT=3306
DB_NAME=lawvriksh_prod
DB_USER=lawvriksh_user
DB_PASSWORD=LawVriksh2024!SecureDB#Pass
MYSQL_ROOT_PASSWORD=RootLawVriksh2024!SuperSecure#MySQL

# Database URL for SQLAlchemy
DATABASE_URL=mysql+pymysql://lawvriksh_user:LawVriksh2024!SecureDB#Pass@mysql:3306/lawvriksh_prod

# =============================================================================
# REDIS CACHE CONFIGURATION
# =============================================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=LawVriksh2024!Redis#Cache
REDIS_URL=redis://:LawVriksh2024!Redis#Cache@redis:6379/0

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret Key (Generate with: openssl rand -hex 32)
JWT_SECRET_KEY=a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456

# Application Secret Key (Generate with: openssl rand -hex 32)
SECRET_KEY=9876543210fedcba0987654321fedcba0987654321fedcba0987654321fedcba

# Password Salt (Generate with: openssl rand -hex 16)
PASSWORD_SALT=abcdef1234567890fedcba0987654321

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
# SMTP Settings (Update with your email provider)


EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=Lawvriksh@123
# Email Templates
WELCOME_EMAIL_TEMPLATE=welcome_template
FEEDBACK_EMAIL_TEMPLATE=feedback_template

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
CACHE_DIR=/app/cache

# Security Headers
ALLOWED_HOSTS=lawvriksh.com,www.lawvriksh.com
CORS_ORIGINS=https://lawvriksh.com,https://www.lawvriksh.com

# Session Configuration
SESSION_TIMEOUT=3600
SESSION_SECURE=true
SESSION_HTTPONLY=true

# =============================================================================
# CELERY CONFIGURATION (Background Tasks)
# =============================================================================
CELERY_BROKER_URL=redis://:LawVriksh2024!Redis#Cache@redis:6379/1
CELERY_RESULT_BACKEND=redis://:LawVriksh2024!Redis#Cache@redis:6379/2

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=16777216  # 16MB in bytes
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx
UPLOAD_DIR=/app/uploads

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600  # 1 hour in seconds

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_RETENTION_DAYS=30

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================
SSL_ENABLED=true
SSL_CERT_PATH=/etc/ssl/certs/lawvriksh.crt
SSL_KEY_PATH=/etc/ssl/private/lawvriksh.key

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=lawvriksh-backups

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================
# Database Connection Pool
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# Cache Settings
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Worker Settings
WORKER_PROCESSES=4
WORKER_CONNECTIONS=1000
WORKER_TIMEOUT=120

# =============================================================================
# FEATURE FLAGS
# =============================================================================
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_SOCIAL_LOGIN=false
ENABLE_TWO_FACTOR_AUTH=false

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# Google Analytics (Optional)
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Sentry Error Tracking (Optional)
SENTRY_DSN=https://your-sentry-dsn-here

# =============================================================================
# DEVELOPMENT OVERRIDES (Keep false in production)
# =============================================================================
ENABLE_CORS=true
ENABLE_DEBUG_TOOLBAR=false
ENABLE_PROFILING=false
